/**
 * Chat Application JavaScript
 */

const ChatApp = {
    currentConversationId: null,
    currentPage: 1,
    hasMoreMessages: false,
    isLoadingMessages: false,

    pusherChannel: null,

    /**
     * Initialize the chat application
     */
    init: function(conversationId = null) {
        this.loadConversations();
        this.bindEvents();
        this.subscribeToUserStatusChannel();

        if (conversationId) {
            // Use selectConversation instead of loadMessages directly
            this.selectConversation(conversationId);
        }
    },

    /**
     * Bind event listeners
     */
    bindEvents: function() {
        const self = this;

        // Send message on Enter key (but not Shift+Enter)
        $(document).on('keydown', '.message-input', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                self.sendMessage();
            }
        });

        // Send button click
        $(document).on('click', '.send-btn', function() {
            self.sendMessage();
        });

        // File attachment button
        $(document).on('click', '.attach-btn, .attachment-btn', function() {
            $('#fileUploadModal').modal('show');
        });

        // Conversation item click
        $(document).on('click', '.conversation-item', function() {
            const conversationId = $(this).data('conversation-id'); // Database ID for internal use
            const conversationIds = $(this).data('conversation-ids'); // IDS field for URL

            // Update URL with conversation IDS (not database ID)
            const url = new URL(window.location);
            url.searchParams.set('conversation_id', conversationIds);
            window.history.pushState({}, '', url);

            self.selectConversation(conversationId);

            // Update envelope counter when conversation is clicked
            // This ensures dashboard layout users get counter updates
            setTimeout(() => {
                self.updateEnvelopeCounterDirect();
            }, 1000);
        });

        // Load more messages
        $(document).on('click', '.load-more-btn', function() {
            self.loadMoreMessages();
        });

        // File input change
        $(document).on('change', '#fileInput', function() {
            self.previewFiles(this.files);
        });

        // Send files button
        $(document).on('click', '#sendFilesBtn', function() {
            self.sendFiles();
        });

        // Emoji click events (using event delegation to prevent multiple bindings)
        $(document).on('click', '.emoji-item', function() {
            const emoji = $(this).data('emoji');
            const messageInput = $('.message-input');
            const currentValue = messageInput.val();
            const cursorPos = messageInput[0].selectionStart;

            const newValue = currentValue.slice(0, cursorPos) + emoji + currentValue.slice(cursorPos);
            messageInput.val(newValue);

            // Set cursor position after emoji
            messageInput[0].setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);
            messageInput.focus();

            // Hide emoji picker
            $('.emoji-picker-container').hide();
        });



        // Auto-resize textarea
        $(document).on('input', '.message-input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });

        // Emoji picker button
        $(document).on('click', '.emoji-btn', function() {
            self.toggleEmojiPicker();
        });

        // Click outside to close emoji picker
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.emoji-picker-container, .emoji-btn').length) {
                $('.emoji-picker-container').hide();
            }
        });

        // Archive conversation
        $(document).on('click', '.conversation-action-btn.archive', function(e) {
            e.stopPropagation();
            const conversationId = $(this).data('conversation-id');
            self.archiveConversation(conversationId);
        });

        // Delete conversation
        $(document).on('click', '.conversation-action-btn.delete', function(e) {
            e.stopPropagation();
            const conversationId = $(this).data('conversation-id');
            if (confirm('Are you sure you want to delete this chat?')) {
                self.deleteConversation(conversationId);
            }
        });

        // Search conversations
        $(document).on('input', '#chat-search-input', function() {
            const searchTerm = $(this).val().toLowerCase();
            self.filterConversations(searchTerm);
        });
    },

    /**
     * Load conversations list
     */
    loadConversations: function() {
        const self = this;

        $.ajax({
            url: '/chats/conversations',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    self.renderConversations(response.conversations);
                }
            },
            error: function(xhr) {
                console.error('Error loading conversations:', xhr);
                $('#conversations-list').html('<div class="text-center p-4 text-muted">Error loading conversations</div>');
            }
        });
    },

    /**
     * Render conversations in sidebar
     */
    renderConversations: function(conversations) {
        const conversationsList = $('#conversations-list');

        if (conversations.length === 0) {
            conversationsList.html('<div class="text-center p-4 text-muted">No conversations yet</div>');
            return;
        }

        let html = '';
        conversations.forEach(conversation => {
            const isActive = conversation.id == this.currentConversationId ? 'active' : '';
            // Don't show unread badge for active conversation
            const unreadBadge = (conversation.unread_count > 0 && !isActive) ?
                `<span class="unread-badge">${conversation.unread_count}</span>` : '';

            const lastMessage = conversation.last_message ?
                (conversation.last_message.is_sender ? 'You: ' : `${conversation.last_message.sender_name}: `) + this.truncateMessage(conversation.last_message.content, 50) :
                'No messages yet';

            const profilePic = conversation.other_user.profile_pic ?
                `/website/${conversation.other_user.profile_pic}` :
                '/website/assets/images/default-avatar.png';

            const timeAgo = conversation.last_message && conversation.last_message.created_at ?
                this.formatTimeAgo(conversation.last_message.created_at) : '';

            // Determine online status
            const isOnline = conversation.other_user.is_online;
            const onlineStatus = isOnline ? 'online' : 'offline';
            const onlineIndicator = `<div class="online-indicator ${onlineStatus}"></div>`;

            html += `
                <div class="conversation-item ${isActive}" data-conversation-id="${conversation.id}" data-conversation-ids="${conversation.ids}" data-other-user-id="${conversation.other_user.id}">
                    <div class="position-relative">
                        <img src="${profilePic}" alt="${conversation.other_user.name}" class="conversation-avatar">
                        ${onlineIndicator}
                    </div>
                    <div class="conversation-info">
                        <div class="conversation-header">
                            <div class="conversation-name">${conversation.other_user.name}</div>
                            <div class="conversation-time">${timeAgo}</div>
                        </div>
                        <div class="conversation-preview">${lastMessage}</div>
                        ${unreadBadge ? `<div class="mt-1">${unreadBadge}</div>` : ''}
                    </div>
                    <div class="conversation-actions">
                        <button class="conversation-action-btn archive" title="Archive" data-conversation-id="${conversation.id}">
                            <i class="fas fa-archive"></i>
                        </button>
                        <button class="conversation-action-btn delete" title="Delete Chat" data-conversation-id="${conversation.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        conversationsList.html(html);
    },

    /**
     * Truncate message for preview
     */
    truncateMessage: function(message, maxLength) {
        if (message.length <= maxLength) {
            return message;
        }
        return message.substring(0, maxLength) + '...';
    },

    /**
     * Format time ago
     */
    formatTimeAgo: function(dateString) {
        if (!dateString) {
            return '';
        }

        const date = new Date(dateString);

        // Check if date is valid
        if (isNaN(date.getTime())) {
            return '';
        }

        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) {
            return 'now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `${minutes}m`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours}h`;
        } else if (diffInSeconds < 604800) {
            const days = Math.floor(diffInSeconds / 86400);
            return `${days}d`;
        } else {
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        }
    },

    /**
     * Select a conversation
     */
    selectConversation: function(conversationId) {
        this.currentConversationId = conversationId;
        this.currentPage = 1;

        // Update active state
        $('.conversation-item').removeClass('active');
        $(`.conversation-item[data-conversation-id="${conversationId}"]`).addClass('active');

        // Subscribe to Pusher channel for this conversation
        this.subscribeToPusherChannel(conversationId);

        // Load messages
        this.loadMessages(conversationId);

        // Mark conversation as read since it's now open
        this.markAsRead(conversationId);

        // Refresh conversations to update unread counts
        setTimeout(() => {
            this.loadConversations();
        }, 500);
    },

    /**
     * Load messages for a conversation
     */
    loadMessages: function(conversationId, page = 1) {
        const self = this;

        if (this.isLoadingMessages) return;
        this.isLoadingMessages = true;

        $.ajax({
            url: `/chats/messages/${conversationId}?page=${page}`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    if (page === 1) {
                        self.currentConversationData = response.conversation;
                        self.renderChatHeader(response.conversation);
                        self.renderMessages(response.messages);
                        self.renderChatInput();
                        self.subscribeToPusherChannel(conversationId);
                        self.scrollToBottom();
                    } else {
                        self.prependMessages(response.messages);
                    }

                    self.hasMoreMessages = response.has_more;
                    self.currentPage = response.current_page;

                    // Mark as read and update message statuses
                    self.markAsRead(conversationId);

                    // Also manually update all sent messages to read status
                    setTimeout(() => {
                        $('.message-bubble.sent').each(function() {
                            const messageId = $(this).data('message-id');
                            if (messageId) {
                                self.updateMessageStatus(messageId, 'read');
                            }
                        });

                        // Update envelope counter after marking messages as read
                        self.updateEnvelopeCounterDirect();
                    }, 600);

                    self.updateMessageStatusesForSender(conversationId);
                }
                self.isLoadingMessages = false;
            },
            error: function(xhr) {
                console.error('Error loading messages:', xhr);
                self.isLoadingMessages = false;
            }
        });
    },

    /**
     * Render chat header
     */
    renderChatHeader: function(conversation) {
        const profilePic = conversation.other_user.profile_pic ?
            `/website/${conversation.other_user.profile_pic}` :
            '/website/assets/images/default-avatar.png';

        // Determine online status and last seen
        const isOnline = conversation.other_user.is_online;
        const onlineStatus = isOnline ? 'online' : 'offline';
        const onlineIndicator = `<div class="online-indicator ${onlineStatus}"></div>`;

        let statusText = '';
        if (isOnline) {
            statusText = '<span style="color: #28a745;">Online</span>';
        } else if (conversation.other_user.online_at) {
            statusText = `Last seen ${this.formatTimeAgo(conversation.other_user.online_at)}`;
        } else {
            statusText = 'Last seen recently';
        }

        const headerHtml = `
            <div class="chat-header">
                <div class="chat-header-left">
                    <div class="position-relative">
                        <img src="${profilePic}" alt="${conversation.other_user.name}" class="chat-header-avatar">
                        ${onlineIndicator}
                    </div>
                    <div class="chat-header-info">
                        <h6>${conversation.other_user.name}</h6>
                        <p class="last-seen">${statusText}</p>
                    </div>
                </div>
                <div class="chat-header-actions">
                    <button type="button" class="chat-header-btn" title="More options">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
            </div>
        `;

        const messagesHtml = `
            <div class="chat-messages" id="chat-messages">
                ${this.hasMoreMessages ? '<div class="load-more-btn">Load more messages</div>' : ''}
            </div>
        `;

        const inputHtml = `
            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <button type="button" class="attachment-btn" title="Attach files">
                        <i class="fas fa-plus"></i>
                    </button>
                    <textarea class="message-input" placeholder="Write message here..." rows="1"></textarea>
                    <button type="button" class="emoji-btn" title="Add emoji">
                        <i class="fas fa-smile"></i>
                    </button>
                    <button type="button" class="send-btn" title="Send message">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
            <div class="emoji-picker-container">
                <div class="emoji-picker-simple">
                    <div class="emoji-grid">
                        <span class="emoji-item" data-emoji="😀">😀</span>
                        <span class="emoji-item" data-emoji="😃">😃</span>
                        <span class="emoji-item" data-emoji="😄">😄</span>
                        <span class="emoji-item" data-emoji="😁">😁</span>
                        <span class="emoji-item" data-emoji="😆">😆</span>
                        <span class="emoji-item" data-emoji="😅">😅</span>
                        <span class="emoji-item" data-emoji="😂">😂</span>
                        <span class="emoji-item" data-emoji="🤣">🤣</span>
                        <span class="emoji-item" data-emoji="😊">😊</span>
                        <span class="emoji-item" data-emoji="😇">😇</span>
                        <span class="emoji-item" data-emoji="🙂">🙂</span>
                        <span class="emoji-item" data-emoji="🙃">🙃</span>
                        <span class="emoji-item" data-emoji="😉">😉</span>
                        <span class="emoji-item" data-emoji="😌">😌</span>
                        <span class="emoji-item" data-emoji="😍">😍</span>
                        <span class="emoji-item" data-emoji="🥰">🥰</span>
                        <span class="emoji-item" data-emoji="😘">😘</span>
                        <span class="emoji-item" data-emoji="😗">😗</span>
                        <span class="emoji-item" data-emoji="😙">😙</span>
                        <span class="emoji-item" data-emoji="😚">😚</span>
                        <span class="emoji-item" data-emoji="😋">😋</span>
                        <span class="emoji-item" data-emoji="😛">😛</span>
                        <span class="emoji-item" data-emoji="😝">😝</span>
                        <span class="emoji-item" data-emoji="😜">😜</span>
                        <span class="emoji-item" data-emoji="🤪">🤪</span>
                        <span class="emoji-item" data-emoji="🤨">🤨</span>
                        <span class="emoji-item" data-emoji="🧐">🧐</span>
                        <span class="emoji-item" data-emoji="🤓">🤓</span>
                        <span class="emoji-item" data-emoji="😎">😎</span>
                        <span class="emoji-item" data-emoji="🤩">🤩</span>
                        <span class="emoji-item" data-emoji="🥳">🥳</span>
                        <span class="emoji-item" data-emoji="😏">😏</span>
                        <span class="emoji-item" data-emoji="😒">😒</span>
                        <span class="emoji-item" data-emoji="😞">😞</span>
                        <span class="emoji-item" data-emoji="😔">😔</span>
                        <span class="emoji-item" data-emoji="😟">😟</span>
                        <span class="emoji-item" data-emoji="😕">😕</span>
                        <span class="emoji-item" data-emoji="🙁">🙁</span>
                        <span class="emoji-item" data-emoji="☹️">☹️</span>
                        <span class="emoji-item" data-emoji="😣">😣</span>
                        <span class="emoji-item" data-emoji="😖">😖</span>
                        <span class="emoji-item" data-emoji="😫">😫</span>
                        <span class="emoji-item" data-emoji="😩">😩</span>
                        <span class="emoji-item" data-emoji="🥺">🥺</span>
                        <span class="emoji-item" data-emoji="😢">😢</span>
                        <span class="emoji-item" data-emoji="😭">😭</span>
                        <span class="emoji-item" data-emoji="😤">😤</span>
                        <span class="emoji-item" data-emoji="😠">😠</span>
                        <span class="emoji-item" data-emoji="😡">😡</span>
                        <span class="emoji-item" data-emoji="❤️">❤️</span>
                        <span class="emoji-item" data-emoji="💙">💙</span>
                        <span class="emoji-item" data-emoji="💚">💚</span>
                        <span class="emoji-item" data-emoji="💛">💛</span>
                        <span class="emoji-item" data-emoji="💜">💜</span>
                        <span class="emoji-item" data-emoji="🖤">🖤</span>
                        <span class="emoji-item" data-emoji="🤍">🤍</span>
                        <span class="emoji-item" data-emoji="👍">👍</span>
                        <span class="emoji-item" data-emoji="👎">👎</span>
                        <span class="emoji-item" data-emoji="👌">👌</span>
                        <span class="emoji-item" data-emoji="✌️">✌️</span>
                        <span class="emoji-item" data-emoji="👏">👏</span>
                        <span class="emoji-item" data-emoji="🙌">🙌</span>
                        <span class="emoji-item" data-emoji="🙏">🙏</span>
                    </div>
                </div>
            </div>

        `;

        $('#chat-area').html(headerHtml + messagesHtml + inputHtml);
        this.bindInputEvents();
    },

    /**
     * Render chat input area
     */
    renderChatInput: function() {
        // Input is already rendered in renderChatHeader, just bind events
        this.bindInputEvents();
    },

    /**
     * Render messages
     */
    renderMessages: function(messages) {
        let html = this.hasMoreMessages ? '<div class="load-more-btn">Load more messages</div>' : '';

        messages.forEach(message => {
            html += this.renderMessage(message);
        });

        $('#chat-messages').html(html);

        // Auto-scroll to bottom after rendering messages, wait for images to load
        const $images = $('#chat-messages img');
        if ($images.length > 0) {
            let loadedImages = 0;
            const self = this;
            $images.on('load', function() {
                loadedImages++;
                if (loadedImages === $images.length) {
                    self.scrollToBottom();
                }
            });

            // Fallback scroll after 1.5 seconds
            setTimeout(() => {
                this.scrollToBottom();
            }, 1500);
        } else {
            this.scrollToBottom();
        }
    },

    /**
     * Prepend messages (for load more)
     */
    prependMessages: function(messages) {
        let html = '';
        messages.forEach(message => {
            html += this.renderMessage(message);
        });

        if (this.hasMoreMessages) {
            html = '<div class="load-more-btn">Load more messages</div>' + html;
        }

        const chatMessages = $('#chat-messages');
        const scrollHeight = chatMessages[0].scrollHeight;

        chatMessages.prepend(html);

        // Maintain scroll position
        chatMessages.scrollTop(chatMessages[0].scrollHeight - scrollHeight);
    },

    /**
     * Render a single message
     */
    renderMessage: function(message) {
        // Determine if current user is the sender
        const isSender = message.sender.id == this.currentUserId;
        const bubbleClass = isSender ? 'sent' : 'received';
        const profilePic = message.sender.profile_pic ?
            `/website/${message.sender.profile_pic}` :
            '/website/assets/images/default-avatar.png';

        let attachmentsHtml = '';
        if (message.attachments && message.attachments.length > 0) {
            message.attachments.forEach(attachment => {
                console.log('Attachment:', attachment); // Debug log

                if (attachment.type === 'image') {
                    attachmentsHtml += `
                        <div class="image-attachment" onclick="window.open('${attachment.url}', '_blank')">
                            <img src="${attachment.url}" alt="${attachment.name}" class="attachment-preview"
                                 style="max-width: 250px; max-height: 200px; border-radius: 8px; cursor: pointer;">
                        </div>
                    `;
                } else if (attachment.type === 'video') {
                    attachmentsHtml += `
                        <div class="video-attachment" onclick="window.open('${attachment.url}', '_blank')">
                            <video controls style="max-width: 250px; max-height: 200px; border-radius: 8px;">
                                <source src="${attachment.url}" type="${attachment.mime_type}">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    `;
                } else {
                    // Document/file attachment
                    const fileIcon = this.getFileIconForAttachment(attachment);
                    attachmentsHtml += `
                        <div class="file-attachment" onclick="window.open('${attachment.url}', '_blank')"
                             style="display: flex; align-items: center; padding: 12px; background: rgba(255,255,255,0.1); border-radius: 8px; cursor: pointer; margin: 4px 0;">
                            <div class="file-icon" style="width: 40px; height: 40px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px; font-size: 18px; color: white; background: #007bff;">
                                ${fileIcon}
                            </div>
                            <div style="flex: 1; color: white;">
                                <div style="font-size: 14px; font-weight: 500;">${attachment.name}</div>
                                <div style="font-size: 12px; opacity: 0.8;">${this.formatFileSize(attachment.size)}</div>
                            </div>
                        </div>
                    `;
                }
            });
        }

        // Only show status icons for messages sent by current user
        const statusIcon = isSender ? this.getStatusIcon(message.sending_status) : '';

        // Determine what content to show based on message type
        let messageContentHtml = '';
        if (message.message_type === 'file') {
            // For file messages, only show attachments
            messageContentHtml = attachmentsHtml;
        } else {
            // For text messages, show content and any attachments
            messageContentHtml = message.content + (attachmentsHtml ? '<br>' + attachmentsHtml : '');
        }

        return `
            <div class="message-bubble ${bubbleClass} fade-in" data-message-id="${message.id}">
                <div class="message-content">
                    ${messageContentHtml}
                </div>
                <div class="message-time">
                    ${message.formatted_time}
                    ${statusIcon}
                </div>
            </div>
        `;
    },

    /**
     * Get status icon for message
     */
    getStatusIcon: function(status) {
        switch (status) {
            case 'sending':
                return '<i class="fas fa-clock" title="Sending"></i>';
            case 'sent':
                return '<i class="fas fa-check" title="Sent"></i>';
            case 'delivered':
                return '<i class="fas fa-check-double" title="Delivered"></i>';
            case 'read':
                return '<i class="fas fa-check-double text-primary" title="Read"></i>';
            default:
                return '';
        }
    },

    /**
     * Send a message
     */
    sendMessage: function() {
        const messageInput = $('.message-input');
        const content = messageInput.val().trim();

        if (!content || !this.currentConversationId) return;

        const formData = new FormData();
        formData.append('conversation_id', this.currentConversationId);
        formData.append('content', content);
        formData.append('message_type', 'text');
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        // Clear input
        messageInput.val('').css('height', 'auto');

        // Add temporary message immediately for sender
        this.addTemporaryMessage(content);

        $.ajax({
            url: '/chats/send',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Remove temporary message and add real one
                    $('.message-bubble.temp').remove();
                    $('#chat-messages').append(ChatApp.renderMessage(response.message));
                    ChatApp.scrollToBottom(true);

                    // Update sidebar
                    setTimeout(() => {
                        ChatApp.loadConversations();
                    }, 500);
                }
            },
            error: function(xhr) {
                console.error('Error sending message:', xhr);
                $('.message-bubble.temp').remove();
                // Show error message
            }
        });
    },

    /**
     * Add temporary message while sending
     */
    addTemporaryMessage: function(content) {
        const tempMessage = `
            <div class="message-bubble sent temp fade-in">
                <div class="message-content">
                    ${content}
                </div>
                <div class="message-time">
                    <i class="fas fa-clock" title="Sending"></i>
                </div>
            </div>
        `;

        $('#chat-messages').append(tempMessage);
        this.scrollToBottom();
    },

    /**
     * Load more messages
     */
    loadMoreMessages: function() {
        if (this.hasMoreMessages && !this.isLoadingMessages) {
            this.loadMessages(this.currentConversationId, this.currentPage + 1);
        }
    },

    /**
     * Mark conversation as read
     */
    markAsRead: function(conversationId) {
        $.ajax({
            url: `/chats/mark-read/${conversationId}`,
            method: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                // Update unread badge
                $(`.conversation-item[data-conversation-id="${conversationId}"] .unread-badge`).remove();

                // Update all sent messages in this conversation to read status
                $('.message-bubble.sent').each(function() {
                    const messageId = $(this).data('message-id');
                    if (messageId) {
                        ChatApp.updateMessageStatus(messageId, 'read');
                    }
                });

                // Update envelope counter - direct approach for dashboard layout
                self.updateEnvelopeCounterDirect();
            },
            error: function(xhr, status, error) {
                console.error('markAsRead error:', error, xhr.responseText);
            }
        });
    },



    /**
     * Preview selected files
     */
    previewFiles: function(files) {
        const preview = $('#filePreview');
        preview.empty();

        let imageCount = 0;
        let videoCount = 0;
        let docCount = 0;

        Array.from(files).forEach(file => {
            const fileType = this.getFileType(file.type);

            // Check limits
            if (fileType === 'image' && imageCount >= 10) return;
            if (fileType === 'video' && videoCount >= 3) return;
            if (fileType === 'document' && docCount >= 5) return;

            const filePreview = $(`
                <div class="file-preview-item d-flex align-items-center mb-2 p-2 border rounded">
                    <div class="file-icon me-2">
                        <i class="fas fa-${this.getFileIcon(fileType)}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${file.name}</div>
                        <div class="text-muted small">${this.formatFileSize(file.size)}</div>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-file" data-index="${preview.children().length}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `);

            preview.append(filePreview);

            if (fileType === 'image') imageCount++;
            if (fileType === 'video') videoCount++;
            if (fileType === 'document') docCount++;
        });

        // Remove file event
        $('.remove-file').on('click', function() {
            $(this).closest('.file-preview-item').remove();
        });
    },

    /**
     * Send files
     */
    sendFiles: function() {
        const fileInput = $('#fileInput')[0];
        const files = fileInput.files;

        if (files.length === 0 || !this.currentConversationId) return;

        const formData = new FormData();
        formData.append('conversation_id', this.currentConversationId);
        formData.append('message_type', 'file');
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        Array.from(files).forEach(file => {
            formData.append('attachments[]', file);
        });

        // Show loading
        $('#sendFilesBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Sending...');

        $.ajax({
            url: '/chats/send',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    $('#chat-messages').append(ChatApp.renderMessage(response.message));
                    ChatApp.scrollToBottom();
                    ChatApp.loadConversations();
                    $('#fileUploadModal').modal('hide');
                    $('#fileInput').val('');
                    $('#filePreview').empty();
                }
                $('#sendFilesBtn').prop('disabled', false).html('Send Files');
            },
            error: function(xhr) {
                console.error('Error sending files:', xhr);
                $('#sendFilesBtn').prop('disabled', false).html('Send Files');
            }
        });
    },

    /**
     * Get file type from mime type
     */
    getFileType: function(mimeType) {
        if (mimeType.startsWith('image/')) return 'image';
        if (mimeType.startsWith('video/')) return 'video';
        return 'document';
    },

    /**
     * Get file icon based on type
     */
    getFileIcon: function(type) {
        switch (type) {
            case 'image': return 'image';
            case 'video': return 'video';
            default: return 'file';
        }
    },

    /**
     * Get file icon HTML for attachment object
     */
    getFileIconForAttachment: function(attachment) {
        const name = attachment.name ? attachment.name.toLowerCase() : '';
        const mimeType = attachment.mime_type ? attachment.mime_type.toLowerCase() : '';

        // PDF files
        if (mimeType.includes('pdf') || name.endsWith('.pdf')) {
            return '<i class="fas fa-file-pdf" style="color: #dc3545;"></i>';
        }

        // Word documents
        if (mimeType.includes('word') || name.endsWith('.doc') || name.endsWith('.docx')) {
            return '<i class="fas fa-file-word" style="color: #2b579a;"></i>';
        }

        // Excel files
        if (mimeType.includes('excel') || mimeType.includes('spreadsheet') || name.endsWith('.xls') || name.endsWith('.xlsx')) {
            return '<i class="fas fa-file-excel" style="color: #217346;"></i>';
        }

        // PowerPoint files
        if (mimeType.includes('presentation') || name.endsWith('.ppt') || name.endsWith('.pptx')) {
            return '<i class="fas fa-file-powerpoint" style="color: #d24726;"></i>';
        }

        // Archive files
        if (mimeType.includes('zip') || mimeType.includes('rar') || name.endsWith('.zip') || name.endsWith('.rar') || name.endsWith('.7z')) {
            return '<i class="fas fa-file-archive" style="color: #6c757d;"></i>';
        }

        // Text files
        if (mimeType.includes('text') || name.endsWith('.txt')) {
            return '<i class="fas fa-file-alt" style="color: #6c757d;"></i>';
        }

        // Default file icon
        return '<i class="fas fa-file" style="color: #6c757d;"></i>';
    },

    /**
     * Format file size
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Scroll to bottom of messages
     */
    scrollToBottom: function(smooth = false) {
        const chatMessages = $('#chat-messages');
        if (chatMessages.length > 0) {
            // Use setTimeout to ensure DOM is updated before scrolling
            setTimeout(() => {
                if (smooth) {
                    chatMessages.animate({
                        scrollTop: chatMessages[0].scrollHeight
                    }, 300);
                } else {
                    chatMessages.scrollTop(chatMessages[0].scrollHeight);
                }
            }, 100);
        }
    },

    /**
     * Subscribe to Pusher channel for conversation
     */
    subscribeToPusherChannel: function(conversationId) {
        if (!window.pusher) {
            return;
        }

        try {
            // Unsubscribe from previous channel
            if (this.pusherChannel) {
                window.pusher.unsubscribe(this.pusherChannel.name);
            }

            // Subscribe to new channel
            this.pusherChannel = window.pusher.subscribe(`conversation.${conversationId}`);

            // Listen for new messages
            this.pusherChannel.bind('message.sent', (data) => {
                console.log('Pusher message.sent received:', data);

                // Check if this conversation is currently open by comparing with URL
                const urlParams = new URLSearchParams(window.location.search);
                const urlConversationId = urlParams.get('conversation_id');
                const isConversationOpen = (urlConversationId === data.conversation_ids);

                this.handleNewMessage(data.message);

                // Always update sidebar - either update existing conversation or reload all
                this.updateConversationInSidebar(data.message);

                // Auto-update message status based on user activity
                if (data.message.sender.id != this.currentUserId) {
                    // Always mark as delivered since user is online (receiving the event)
                    this.markAsDelivered(data.message.id);

                    // If this conversation is currently open (based on URL), also mark as read
                    if (isConversationOpen) {
                        this.markAsRead(this.currentConversationId);
                    } else {
                        // Update envelope counter for new messages when conversation is not open
                        if (typeof updateEnvelopeCounter === 'function') {
                            updateEnvelopeCounter();
                        }
                    }
                }
            });

            // Listen for message status updates
            this.pusherChannel.bind('message.delivered', (data) => {
                console.log('Received message.delivered event:', data);
                this.updateMessageStatus(data.message_id, 'delivered');
            });

            this.pusherChannel.bind('message.read', (data) => {
                data.message_ids.forEach(messageId => {
                    this.updateMessageStatus(messageId, 'read');
                });

                // Update envelope counter when messages are read
                ChatApp.updateEnvelopeCounterDirect();
            });



            // Listen for user online/offline status
            this.pusherChannel.bind('user.online', (data) => {
                this.updateUserOnlineStatus(data.user_id, true);
            });

            this.pusherChannel.bind('user.offline', (data) => {
                this.updateUserOnlineStatus(data.user_id, false, data.last_seen);
            });

        } catch (error) {
            console.error('Error subscribing to Pusher channel:', error);
        }
    },

    /**
     * Handle new message from Pusher
     */
    handleNewMessage: function(message) {
        console.log('handleNewMessage called with:', message);
        console.log('Current conversation ID:', this.currentConversationId);
        console.log('Message conversation ID:', message.conversation_id);
        console.log('Current user ID:', this.currentUserId);
        console.log('Message sender ID:', message.sender.id);

        // Only add message to chat if it's for the current conversation
        if (message.conversation_id == this.currentConversationId) {
            // Check if message is from current user
            if (message.sender.id == this.currentUserId) {
                // For messages from current user, don't add via Pusher
                // They already got it via AJAX response
                console.log('Ignoring own message from Pusher to avoid duplicate');
            } else {
                // For messages from other users, check for duplicates first
                const existingMessage = $(`.message-bubble[data-message-id="${message.id}"]`);
                if (existingMessage.length === 0) {
                    console.log('Adding message from other user:', message.id);
                    const messageHtml = this.renderMessage(message);
                    $('#chat-messages').append(messageHtml);

                    // For file messages, wait for images to load before scrolling
                    const $newMessage = $('#chat-messages .message-bubble:last');
                    const $images = $newMessage.find('img');

                    if ($images.length > 0) {
                        // Wait for images to load
                        let loadedImages = 0;
                        const self = this;
                        $images.on('load', function() {
                            loadedImages++;
                            if (loadedImages === $images.length) {
                                self.scrollToBottom(true);
                            }
                        });

                        // Fallback scroll after 1 second
                        setTimeout(() => {
                            this.scrollToBottom(true);
                        }, 1000);
                    } else {
                        this.scrollToBottom(true);
                    }

                    // Mark as delivered immediately
                    this.markAsDelivered(message.id);

                    // Mark as read since conversation is open
                    this.markAsRead(this.currentConversationId);
                } else {
                    console.log('Message already exists, skipping duplicate:', message.id);
                }
            }
        }

        // Update sidebar
        this.loadConversations();
    },

    /**
     * Update message status
     */
    updateMessageStatus: function(messageId, status) {
        console.log(`Updating message ${messageId} status to ${status}`);
        const messageElement = $(`.message-bubble[data-message-id="${messageId}"] .message-time`);
        console.log(`Found ${messageElement.length} message elements for ID ${messageId}`);

        if (messageElement.length === 0) {
            console.log(`Message element not found for ID ${messageId}, retrying...`);
            // Try alternative selector in case the message is still loading
            setTimeout(() => {
                const retryElement = $(`.message-bubble[data-message-id="${messageId}"] .message-time`);
                console.log(`Retry found ${retryElement.length} message elements for ID ${messageId}`);
                if (retryElement.length > 0) {
                    this.updateMessageStatusElement(retryElement, status);
                } else {
                    console.log(`Still no message element found for ID ${messageId} after retry`);
                }
            }, 100);
            return;
        }

        this.updateMessageStatusElement(messageElement, status);
    },

    updateMessageStatusElement: function(messageElement, status) {
        console.log(`Updating status element to ${status}`);
        const statusIcon = this.getStatusIcon(status);
        console.log(`Generated status icon: ${statusIcon}`);

        // Update status icon
        const existingIcons = messageElement.find('i');
        console.log(`Found ${existingIcons.length} existing icons`);
        existingIcons.remove();

        if (statusIcon) {
            messageElement.append(' ' + statusIcon);
            console.log(`Added new status icon: ${statusIcon}`);
        }
    },



    /**
     * Update conversation in sidebar when new message arrives
     */
    updateConversationInSidebar: function(message) {
        const conversationItem = $(`.conversation-item[data-conversation-id="${message.conversation_id}"]`);

        if (conversationItem.length) {
            // Update last message preview
            conversationItem.find('.conversation-preview').text(message.content);

            // Update time
            conversationItem.find('.conversation-time').text(this.formatTimeAgo(message.created_at));

            // Move conversation to top
            conversationItem.prependTo('#conversations-list');

            // Update unread count if message is not from current user
            if (message.sender.id != this.currentUserId) {
                // Check if this conversation is currently active (don't show badge for active conversation)
                const isActive = conversationItem.hasClass('active');

                if (!isActive) {
                    let unreadBadgeContainer = conversationItem.find('.mt-1');
                    let unreadBadge = conversationItem.find('.unread-badge');

                    if (unreadBadge.length) {
                        const currentCount = parseInt(unreadBadge.text()) || 0;
                        unreadBadge.text(currentCount + 1);
                    } else {
                        // Create new unread badge with proper structure
                        if (unreadBadgeContainer.length) {
                            unreadBadgeContainer.html('<span class="unread-badge">1</span>');
                        } else {
                            conversationItem.find('.conversation-info').append('<div class="mt-1"><span class="unread-badge">1</span></div>');
                        }
                    }
                }
            }
        } else {
            // If conversation doesn't exist in sidebar, reload conversations
            // This happens when it's a new conversation
            setTimeout(() => {
                this.loadConversations();
            }, 100);
        }
    },

    /**
     * Update user online status in real-time
     */
    updateUserOnlineStatus: function(userId, isOnline, lastSeen = null) {
        // Update in sidebar
        $(`.conversation-item`).each(function() {
            const $item = $(this);
            const otherUserId = $item.data('other-user-id'); // We'll need to add this data attribute

            if (otherUserId == userId) {
                const indicator = $item.find('.online-indicator');
                if (isOnline) {
                    indicator.removeClass('offline').addClass('online');
                } else {
                    indicator.removeClass('online').addClass('offline');
                }
            }
        });

        // Update in chat header if this is the current conversation
        const currentConversation = this.getCurrentConversation();
        if (currentConversation && currentConversation.other_user.id == userId) {
            const headerIndicator = $('.chat-header .online-indicator');
            const statusText = $('.chat-header .last-seen');

            if (isOnline) {
                headerIndicator.removeClass('offline').addClass('online');
                statusText.html('<span style="color: #28a745;">Online</span>');
            } else {
                headerIndicator.removeClass('online').addClass('offline');
                const lastSeenText = lastSeen ? `Last seen ${this.formatTimeAgo(lastSeen)}` : 'Last seen recently';
                statusText.text(lastSeenText);
            }
        }
    },

    /**
     * Get current user ID
     */
    getCurrentUserId: function() {
        // You'll need to pass this from the backend or store it globally
        return window.currentUserId || null;
    },

    /**
     * Get current conversation data
     */
    getCurrentConversation: function() {
        return this.currentConversationData || null;
    },

    /**
     * Subscribe to global user status channel
     */
    subscribeToUserStatusChannel: function() {
        if (!window.pusher) {
            return;
        }

        try {
            const userStatusChannel = window.pusher.subscribe('user-status');

            userStatusChannel.bind('user.online', (data) => {
                this.updateUserOnlineStatus(data.user_id, true);
            });

            userStatusChannel.bind('user.offline', (data) => {
                this.updateUserOnlineStatus(data.user_id, false, data.last_seen);
            });

            // User-specific channel is now handled globally in master layout
            // This ensures delivery status works on any page, not just chat page

        } catch (error) {
            console.error('Error subscribing to user status channel:', error);
        }
    },

    /**
     * Bind input events
     */
    bindInputEvents: function() {
        const self = this;

        // Send message on Enter key (but not Shift+Enter)
        $(document).on('keydown', '.message-input', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                self.sendMessage();
            }
        });

        // Send message on button click
        $(document).on('click', '.send-btn', function() {
            self.sendMessage();
        });

        // Auto-resize textarea
        $(document).on('input', '.message-input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });

        // Emoji handlers are already bound in bindEvents() - no need to duplicate them here
    },

    /**
     * Update message statuses for sender (current user's messages)
     */
    updateMessageStatusesForSender: function(conversationId) {
        // This function is no longer needed since the manual fallback works perfectly
        // The manual fallback in loadMessages() handles read status updates correctly
    },



    /**
     * Mark message as delivered
     */
    markAsDelivered: function(messageId) {
        console.log(`Marking message ${messageId} as delivered`);
        $.ajax({
            url: `/chats/messages/${messageId}/delivered`,
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log(`Successfully marked message ${messageId} as delivered:`, response);
                // Update message status visually
                ChatApp.updateMessageStatus(messageId, 'delivered');
            },
            error: function(xhr, status, error) {
                console.error(`Error marking message ${messageId} as delivered:`, error);
                console.error('Response:', xhr.responseText);
            }
        });
    },



    /**
     * Toggle emoji picker
     */
    toggleEmojiPicker: function() {
        const emojiPicker = $('.emoji-picker-container');
        emojiPicker.toggle();

        // Emoji click events are handled by global event delegation (see bindEvents)
    },

    /**
     * Get unread messages count for header
     */
    getUnreadMessagesCount: function() {
        let totalUnread = 0;
        $('.conversation-item .unread-badge').each(function() {
            totalUnread += parseInt($(this).text()) || 0;
        });
        return totalUnread;
    },

    /**
     * Update message counter in header
     */
    updateMessageCounter: function() {
        const count = this.getUnreadMessagesCount();
        let counter = $('.message-counter');

        if (count > 0) {
            if (counter.length === 0) {
                // Create counter if it doesn't exist
                $('a[href*="chats"]').append('<span class="message-counter">' + count + '</span>');
            } else {
                counter.text(count);
            }
        } else {
            counter.remove();
        }
    },





    /**
     * Archive conversation
     */
    archiveConversation: function(conversationId) {
        $.ajax({
            url: `/chats/conversations/${conversationId}/archive`,
            method: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    // Remove from sidebar
                    $(`.conversation-item[data-conversation-id="${conversationId}"]`).fadeOut(300, function() {
                        $(this).remove();
                    });

                    // If this was the active conversation, clear chat area
                    if (ChatApp.currentConversationId == conversationId) {
                        $('#chat-area').html(`
                            <div class="empty-state">
                                <i class="fas fa-comments"></i>
                                <h5>Select a conversation to start messaging</h5>
                                <p class="text-muted">Choose from your existing conversations or start a new one</p>
                            </div>
                        `);
                        ChatApp.currentConversationId = null;
                    }
                }
            },
            error: function(xhr) {
                console.error('Error archiving conversation:', xhr);
                alert('Failed to archive conversation');
            }
        });
    },

    /**
     * Delete conversation
     */
    deleteConversation: function(conversationId) {
        $.ajax({
            url: `/chats/conversations/${conversationId}`,
            method: 'DELETE',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    // Remove from sidebar
                    $(`.conversation-item[data-conversation-id="${conversationId}"]`).fadeOut(300, function() {
                        $(this).remove();
                    });

                    // If this was the active conversation, clear chat area
                    if (ChatApp.currentConversationId == conversationId) {
                        $('#chat-area').html(`
                            <div class="empty-state">
                                <i class="fas fa-comments"></i>
                                <h5>Select a conversation to start messaging</h5>
                                <p class="text-muted">Choose from your existing conversations or start a new one</p>
                            </div>
                        `);
                        ChatApp.currentConversationId = null;
                    }
                }
            },
            error: function(xhr) {
                console.error('Error deleting conversation:', xhr);
                alert('Failed to delete conversation');
            }
        });
    },

    /**
     * Filter conversations based on search term
     */
    filterConversations: function(searchTerm) {
        const conversationItems = $('.conversation-item');

        if (!searchTerm) {
            conversationItems.show();
            return;
        }

        conversationItems.each(function() {
            const $item = $(this);
            const name = $item.find('.conversation-name').text().toLowerCase();
            const preview = $item.find('.conversation-preview').text().toLowerCase();

            if (name.includes(searchTerm) || preview.includes(searchTerm)) {
                $item.show();
            } else {
                $item.hide();
            }
        });
    },

    /**
     * Update envelope counter directly - works for both dashboard and website layouts
     */
    updateEnvelopeCounterDirect: function() {
        $.ajax({
            url: '/chats/unread-count',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    const count = response.count;
                    const counter = $('#envelope-counter');

                    if (count > 0) {
                        counter.text(count > 99 ? '99+' : count).show();
                    } else {
                        counter.hide();
                    }
                }
            },
            error: function() {
                // Silent error handling
            }
        });
    }
};

// File Browser Functionality
const FileBrowser = {
    selectedFiles: [],

    init: function() {
        this.bindEvents();
    },

    bindEvents: function() {
        const self = this;
        const fileInput = document.getElementById('realFileInput');

        // Handle file selection
        if (fileInput) {
            fileInput.addEventListener('change', function(e) {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    // Add new files to existing selection
                    self.selectedFiles = [...self.selectedFiles, ...files];
                    self.displaySelectedFiles(self.selectedFiles);
                    self.updateSelectedPreview();
                }
                // Reset file input to allow selecting same files again
                this.value = '';
            });
        }

        // Handle browse button clicks using event delegation
        $(document).on('click', '#browseFilesBtn, .add-more-files-btn', function() {
            if (fileInput) {
                fileInput.click();
            }
        });

        // Reset modal when closed
        $('#fileUploadModal').on('hidden.bs.modal', function() {
            self.selectedFiles = [];
            self.resetToInitialState();
            self.updateSelectedPreview();
        });

        // Send files button
        $('#sendSelectedBtn').on('click', function() {
            if (self.selectedFiles.length > 0) {
                self.sendFiles();
                $('#fileUploadModal').modal('hide');
            }
        });
    },

    displaySelectedFiles: function(files) {
        const fileBrowserContent = document.getElementById('fileBrowserContent');
        if (!fileBrowserContent) return;

        fileBrowserContent.innerHTML = '';

        files.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-preview-item selected';

            // Create preview based on file type
            if (file.type.startsWith('image/')) {
                // Show image thumbnail
                const reader = new FileReader();
                reader.onload = function(e) {
                    fileItem.innerHTML = `
                        <div class="file-preview-thumbnail">
                            <img src="${e.target.result}" alt="${file.name}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;">
                        </div>
                        <div class="file-info flex-grow-1 ms-3">
                            <h6>${file.name}</h6>
                            <small>${FileBrowser.formatFileSize(file.size)} • ${file.type}</small>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="FileBrowser.removeFile(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                };
                reader.readAsDataURL(file);
            } else if (file.type.startsWith('video/')) {
                // Show video thumbnail
                const video = document.createElement('video');
                video.src = URL.createObjectURL(file);
                video.addEventListener('loadedmetadata', function() {
                    video.currentTime = 1; // Seek to 1 second for thumbnail
                });
                video.addEventListener('seeked', function() {
                    const canvas = document.createElement('canvas');
                    canvas.width = 50;
                    canvas.height = 50;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(video, 0, 0, 50, 50);
                    const thumbnail = canvas.toDataURL();

                    fileItem.innerHTML = `
                        <div class="file-preview-thumbnail position-relative">
                            <img src="${thumbnail}" alt="${file.name}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;">
                            <div class="position-absolute top-50 start-50 translate-middle">
                                <i class="fas fa-play text-white" style="font-size: 12px; background: rgba(0,0,0,0.6); padding: 4px; border-radius: 50%;"></i>
                            </div>
                        </div>
                        <div class="file-info flex-grow-1 ms-3">
                            <h6>${file.name}</h6>
                            <small>${FileBrowser.formatFileSize(file.size)} • ${file.type}</small>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="FileBrowser.removeFile(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    URL.revokeObjectURL(video.src);
                });
            } else {
                // Show file icon for documents
                const iconHtml = this.getFileIconHtml(file);
                fileItem.innerHTML = `
                    <div class="file-icon ${this.getFileType(file)}">
                        ${iconHtml}
                    </div>
                    <div class="file-info flex-grow-1">
                        <h6>${file.name}</h6>
                        <small>${this.formatFileSize(file.size)} • ${file.type || 'Unknown type'}</small>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="FileBrowser.removeFile(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                `;
            }

            fileBrowserContent.appendChild(fileItem);
        });

        // Add browse more button at the end
        const browseMoreBtn = document.createElement('div');
        browseMoreBtn.className = 'text-center py-3 border-top';
        browseMoreBtn.innerHTML = `
            <button type="button" class="btn btn-outline-primary btn-sm add-more-files-btn">
                <i class="fas fa-plus me-2"></i>Add More Files
            </button>
        `;
        fileBrowserContent.appendChild(browseMoreBtn);
    },

    removeFile: function(index) {
        this.selectedFiles.splice(index, 1);

        if (this.selectedFiles.length > 0) {
            this.displaySelectedFiles(this.selectedFiles);
        } else {
            this.resetToInitialState();
        }

        this.updateSelectedPreview();
    },

    resetToInitialState: function() {
        const fileBrowserContent = document.getElementById('fileBrowserContent');
        if (!fileBrowserContent) return;

        fileBrowserContent.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-folder-open text-muted mb-3" style="font-size: 3rem;"></i>
                <p class="text-muted">Click "Browse Files" to select files</p>
                <button type="button" class="btn btn-primary" id="browseFilesBtn">
                    <i class="fas fa-folder-open me-2"></i>Browse Files
                </button>
            </div>
        `;
    },

    getFileIconHtml: function(file) {
        const type = file.type.toLowerCase();
        const name = file.name.toLowerCase();

        // Images
        if (type.includes('image')) return '<i class="fas fa-image"></i>';

        // Videos
        if (type.includes('video')) return '<i class="fas fa-video"></i>';

        // PDF
        if (type.includes('pdf') || name.endsWith('.pdf'))
            return '<span style="font-weight: bold; font-size: 14px;">PDF</span>';

        // Microsoft Word
        if (type.includes('word') || type.includes('document') ||
            name.endsWith('.doc') || name.endsWith('.docx'))
            return '<span style="font-weight: bold; font-size: 12px;">DOC</span>';

        // Microsoft Excel
        if (type.includes('sheet') || type.includes('excel') ||
            name.endsWith('.xls') || name.endsWith('.xlsx'))
            return '<span style="font-weight: bold; font-size: 12px;">XLS</span>';

        // Microsoft PowerPoint
        if (type.includes('presentation') || type.includes('powerpoint') ||
            name.endsWith('.ppt') || name.endsWith('.pptx'))
            return '<span style="font-weight: bold; font-size: 12px;">PPT</span>';

        // Text files
        if (type.includes('text') || name.endsWith('.txt'))
            return '<span style="font-weight: bold; font-size: 12px;">TXT</span>';

        // Code files
        if (name.endsWith('.js') || name.endsWith('.json'))
            return '<span style="font-weight: bold; font-size: 14px;">JS</span>';
        if (name.endsWith('.html') || name.endsWith('.htm'))
            return '<span style="font-weight: bold; font-size: 11px;">HTML</span>';
        if (name.endsWith('.css'))
            return '<span style="font-weight: bold; font-size: 12px;">CSS</span>';
        if (name.endsWith('.php'))
            return '<span style="font-weight: bold; font-size: 12px;">PHP</span>';
        if (name.endsWith('.py'))
            return '<span style="font-weight: bold; font-size: 14px;">PY</span>';
        if (name.endsWith('.java'))
            return '<span style="font-weight: bold; font-size: 11px;">JAVA</span>';
        if (name.endsWith('.cpp') || name.endsWith('.c'))
            return '<span style="font-weight: bold; font-size: 14px;">C++</span>';

        // Archive files
        if (name.endsWith('.zip'))
            return '<span style="font-weight: bold; font-size: 12px;">ZIP</span>';
        if (name.endsWith('.rar'))
            return '<span style="font-weight: bold; font-size: 12px;">RAR</span>';
        if (name.endsWith('.7z'))
            return '<span style="font-weight: bold; font-size: 14px;">7Z</span>';

        // Audio files
        if (type.includes('audio') || name.endsWith('.mp3') || name.endsWith('.wav'))
            return '<i class="fas fa-music"></i>';

        // Default
        return '<i class="fas fa-file"></i>';
    },

    getFileType: function(file) {
        const type = file.type.toLowerCase();
        const name = file.name.toLowerCase();

        // Images
        if (type.includes('image')) return 'image';

        // Videos
        if (type.includes('video')) return 'video';

        // PDF
        if (type.includes('pdf') || name.endsWith('.pdf')) return 'pdf';

        // Microsoft Office
        if (type.includes('word') || type.includes('document') ||
            name.endsWith('.doc') || name.endsWith('.docx')) return 'word';
        if (type.includes('sheet') || type.includes('excel') ||
            name.endsWith('.xls') || name.endsWith('.xlsx')) return 'excel';
        if (type.includes('presentation') || type.includes('powerpoint') ||
            name.endsWith('.ppt') || name.endsWith('.pptx')) return 'powerpoint';

        // Text files
        if (type.includes('text') || name.endsWith('.txt')) return 'text';

        // Code files
        if (name.endsWith('.js') || name.endsWith('.json') ||
            name.endsWith('.html') || name.endsWith('.css') ||
            name.endsWith('.php') || name.endsWith('.py') ||
            name.endsWith('.java') || name.endsWith('.cpp') || name.endsWith('.c')) return 'code';

        // Archive files
        if (name.endsWith('.zip') || name.endsWith('.rar') || name.endsWith('.7z')) return 'archive';

        // Audio files
        if (type.includes('audio') || name.endsWith('.mp3') || name.endsWith('.wav')) return 'audio';

        return 'default';
    },

    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    updateSelectedPreview: function() {
        const preview = document.getElementById('selectedFilesPreview');
        const list = preview ? preview.querySelector('.selected-files-list') : null;
        const sendBtn = document.getElementById('sendSelectedBtn');
        const countSpan = document.getElementById('selectedCount');

        if (!preview || !list || !sendBtn || !countSpan) return;

        if (this.selectedFiles.length > 0) {
            preview.style.display = 'block';
            list.innerHTML = '';

            this.selectedFiles.forEach((file, index) => {
                const chip = document.createElement('span');
                chip.className = 'selected-file-chip';
                chip.innerHTML = `
                    ${file.name}
                    <span class="remove" onclick="FileBrowser.removeFile(${index})">×</span>
                `;
                list.appendChild(chip);
            });

            sendBtn.disabled = false;
            countSpan.textContent = this.selectedFiles.length;
        } else {
            preview.style.display = 'none';
            sendBtn.disabled = true;
            countSpan.textContent = '0';
        }
    },

    sendFiles: function() {
        if (this.selectedFiles.length === 0 || !ChatApp.currentConversationId) return;

        const formData = new FormData();
        formData.append('conversation_id', ChatApp.currentConversationId);

        // Add files to FormData
        this.selectedFiles.forEach((file, index) => {
            formData.append(`files[${index}]`, file);
        });

        // Add CSRF token
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        // Create temporary message bubbles with loading effects
        const tempMessages = this.createTempMessageBubbles();

        // Send files via AJAX
        $.ajax({
            url: '/chats/upload-files',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('Upload response:', response);
                if (response.success) {
                    // Remove temporary messages
                    tempMessages.forEach(temp => temp.remove());

                    // Add the real message using the data from server response
                    if (response.data && response.data.message_data) {
                        console.log('Adding file message to DOM:', response.data.message_data);
                        const messageHtml = ChatApp.renderMessage(response.data.message_data);
                        $('#chat-messages').append(messageHtml);

                        // Verify the message was added with correct data-message-id
                        const addedMessage = $(`.message-bubble[data-message-id="${response.data.message_data.id}"]`);
                        console.log(`File message added to DOM with ID ${response.data.message_data.id}, found ${addedMessage.length} elements`);

                        // For file messages, wait for images to load before scrolling
                        const $newMessage = $('#chat-messages .message-bubble:last');
                        const $images = $newMessage.find('img');

                        if ($images.length > 0) {
                            // Wait for images to load
                            let loadedImages = 0;
                            $images.on('load', function() {
                                loadedImages++;
                                if (loadedImages === $images.length) {
                                    ChatApp.scrollToBottom(true);
                                }
                            });

                            // Fallback scroll after 1 second
                            setTimeout(() => {
                                ChatApp.scrollToBottom(true);
                            }, 1000);
                        } else {
                            ChatApp.scrollToBottom(true);
                        }


                    }

                    // Update sidebar to show latest message
                    if (typeof ChatApp !== 'undefined') {
                        ChatApp.loadConversations();
                    }
                } else {
                    // Show error and remove temp messages
                    tempMessages.forEach(temp => temp.remove());
                    console.error('Upload failed:', response.message);
                    alert('Upload failed: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                // Remove temporary messages on error
                tempMessages.forEach(temp => temp.remove());
                console.error('Upload error:', error);
                console.error('Response:', xhr.responseText);
                alert('Upload error: ' + error + '\nResponse: ' + xhr.responseText);
            }
        });

        // Reset selected files
        this.selectedFiles = [];
    },

    createTempMessageBubbles: function() {
        const messagesContainer = document.querySelector('.chat-messages');
        if (!messagesContainer) return [];

        const tempMessages = [];

        this.selectedFiles.forEach((file, index) => {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message-bubble sent temp-message';

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            if (file.type.startsWith('image/')) {
                // Create blurred image preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    messageContent.innerHTML = `
                        <div class="temp-image-container" style="position: relative;">
                            <img src="${e.target.result}" alt="${file.name}"
                                 style="max-width: 200px; border-radius: 8px; filter: blur(3px); opacity: 0.7;">
                            <div class="loading-overlay" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
                                 background: rgba(0,0,0,0.6); color: white; padding: 10px; border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                                <div class="whatsapp-loader"></div>
                            </div>
                        </div>
                    `;
                };
                reader.readAsDataURL(file);
            } else if (file.type.startsWith('video/')) {
                // Create video placeholder with loading
                messageContent.innerHTML = `
                    <div class="temp-video-container" style="position: relative; background: #f0f0f0; padding: 20px; border-radius: 8px; text-align: center;">
                        <i class="fas fa-video" style="font-size: 2rem; color: #666; opacity: 0.5;"></i>
                        <div style="margin-top: 8px; font-size: 12px; color: #666;">${file.name}</div>
                        <div class="loading-overlay" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
                             background: rgba(0,0,0,0.7); color: white; padding: 10px; border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                            <div class="whatsapp-loader"></div>
                        </div>
                    </div>
                `;
            } else {
                // Create document placeholder with loading
                const iconHtml = this.getFileIconHtml(file);
                messageContent.innerHTML = `
                    <div class="temp-document-container" style="position: relative; display: flex; align-items: center; padding: 12px; background: rgba(255,255,255,0.1); border-radius: 8px; opacity: 0.7;">
                        <div class="file-icon ${this.getFileType(file)}" style="width: 40px; height: 40px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px; font-size: 18px; color: white;">
                            ${iconHtml}
                        </div>
                        <div class="file-info" style="flex: 1; color: white;">
                            <div style="font-weight: 500; font-size: 14px;">${file.name}</div>
                            <div style="font-size: 12px; opacity: 0.8;">${this.formatFileSize(file.size)}</div>
                        </div>
                        <div class="loading-overlay" style="position: absolute; top: 50%; right: 10px; transform: translateY(-50%);
                             background: rgba(0,0,0,0.7); color: white; padding: 8px; border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center;">
                            <div class="whatsapp-loader small"></div>
                        </div>
                    </div>
                `;
            }

            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = 'Sending...';

            messageDiv.appendChild(messageContent);
            messageDiv.appendChild(timeDiv);
            messagesContainer.appendChild(messageDiv);

            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            tempMessages.push(messageDiv);
        });

        return tempMessages;
    }
};

// Initialize FileBrowser when document is ready
$(document).ready(function() {
    FileBrowser.init();
});
