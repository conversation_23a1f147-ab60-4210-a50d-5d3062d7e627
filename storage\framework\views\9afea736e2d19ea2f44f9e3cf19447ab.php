<div>
    <form
        action="<?php echo e(isset($service) ? route('services.update', ['service' => $service->ids, 'type' => 'individual']) : route('services.store', ['type' => 'individual'])); ?>"
        method="POST" enctype="multipart/form-data" class="form-add-services" id="individualServiceForm">
        <?php echo csrf_field(); ?>
        <?php if(isset($service)): ?>
            <?php echo method_field('PUT'); ?>
        <?php endif; ?>
        <div class="row row-gap-5">
            
            <div class="col-md-12">
                <label for="service-name" class="form-label form-input-labels">
                    Service Name<span class="text-danger">*</span>
                </label>
                <input type="text" class="form-control form-inputs-field" placeholder="Enter Service name"
                    id="service-name" name="name" value="<?php echo e(old('name', $service->name ?? '')); ?>">
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            


            
            <div class="col-md-6">
                <label for="category" class="form-label form-input-labels">Category<span
                        class="text-danger">*</span></label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" id="category" name="category_id">
                    <option></option>
                    <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <option value="<?php echo e($category->ids); ?>"
                            <?php echo e(old('category_id', $service->category->ids ?? '') == $category->ids ? 'selected' : ''); ?>>
                            <?php echo e($category->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <option value="">No categories found</option>
                    <?php endif; ?>
                </select>
                <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            


            
            <div class="col-md-6">
                <label for="subcategory" class="form-label form-input-labels">Sub
                    Category<span class="text-danger">*</span></label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" data-allow-clear="true"
                    id="subcategory" name="subcategory_id">
                    <option value="">Select Subcategory</option>
                    <?php if(isset($service) && $service->category): ?>
                        <?php $__empty_1 = true; $__currentLoopData = $service->category->subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <option value="<?php echo e($subcategory->ids); ?>"
                                <?php echo e(old('subcategory_id', $service->subcategory->ids ?? '') == $subcategory->ids ? 'selected' : ''); ?>>
                                <?php echo e($subcategory->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </select>
                <?php $__errorArgs = ['subcategory_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            


            <?php if(auth()->check() && auth()->user()->hasRole('business')): ?>
                <div class="col-md-12">
                    <label for="staff-member" class="form-label form-input-labels">Assign Staff
                        Member<span class="text-danger">*</span></label>
                    <select class="form-select form-select-field" data-control="select2"
                        data-dropdown-css-class="w-619px" data-close-on-select="false"
                        data-placeholder="Select staff members" data-allow-clear="true" multiple="multiple"
                        id="staff-member" name="staff_ids[]">
                        <?php $__empty_1 = true; $__currentLoopData = $staffs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $staff): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <option value="<?php echo e($staff->id); ?>"
                                <?php echo e(isset($service) && $service->staff->contains('id', $staff->id) ? 'selected' : ''); ?>>
                                <?php echo e($staff->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <option disabled>No staff members available</option>
                        <?php endif; ?>
                    </select>
                    <?php $__errorArgs = ['staff_ids'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-danger">
                            <?php echo e($message); ?>

                        </p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            <?php endif; ?>

            
            <div class="col-md-12">
                <label class="form-label form-input-labels">Availability<span class="text-danger">*</span></label>
                <?php if (isset($component)) { $__componentOriginal81b877f93d7de2161525fd317bf6d57d = $component; } ?>
<?php $component = App\View\Components\ServiceAvailabilityComponent::resolve(['availabilities' => $service->availabilities ?? []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('service-availability-component'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\ServiceAvailabilityComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal81b877f93d7de2161525fd317bf6d57d)): ?>
<?php $component = $__componentOriginal81b877f93d7de2161525fd317bf6d57d; ?>
<?php unset($__componentOriginal81b877f93d7de2161525fd317bf6d57d); ?>
<?php endif; ?>
            </div>
            


            
            <div class="col-md-6">
                <label for="duration" class="form-label form-input-labels ">Service Duration<span
                        class="text-danger">*</span></label>
                <select name="duration" id="duration" class="form-control text-muted  form-select-field">
                    <option value="">Select Service Duration</option>
                    <option value="15" <?php echo e(old('duration', $service->duration ?? '') == '15' ? 'selected' : ''); ?>>15
                        min
                    </option>
                    <option value="30" <?php echo e(old('duration', $service->duration ?? '') == '30' ? 'selected' : ''); ?>>30
                        min
                    </option>
                    <option value="45" <?php echo e(old('duration', $service->duration ?? '') == '45' ? 'selected' : ''); ?>>40
                        min
                    </option>
                    <option value="60" <?php echo e(old('duration', $service->duration ?? '') == '60' ? 'selected' : ''); ?>>60
                        min
                    </option>
                </select>
                
                <!-- <select class="form-select form-select-field" id="duration" name="duration"
                    data-control="select2" data-placeholder="Select">
                    <option></option>
                    <option value="30 min">30 min</option>
                    <option value="40 min">40 min</option>
                </select> -->
                <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            


            
            <div class="col-md-3">
                <label for="price" class="form-label form-input-labels">
                    Price <span class="normal opacity-6 light-black">(Inclusive VAT)<span
                            class="text-danger">*</span></span>
                </label>
                <input type="number" class="form-control form-inputs-field" placeholder="Enter price" id="price"
                    name="price" value="<?php echo e(old('price', $service->price ?? '')); ?>">
                <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            

            
            <div class="col-md-3">
                <label for="tax" class="form-label form-input-labels">Additional Costs<span
                        class="text-danger">*</span></label>
                <input type="number" class="form-control form-inputs-field" placeholder="Enter additional cost"
                    id="tax" name="additional_cost"
                    value="<?php echo e(old('additional_cost', $service->additional_cost ?? '')); ?>">
                <?php $__errorArgs = ['additional_cost'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            

            
            <div class="col-md-12">
                <label for="required-items" class="form-label form-input-labels">Required
                    Items For Service<span class="text-danger">*</span></label>
                <input type="text" class="form-control form-inputs-field"
                    placeholder="Enter required items (separate with comma)" id="required-items"
                    name="required_items" value="<?php echo e(old('required_items', $service->required_items ?? '')); ?>">
                <?php $__errorArgs = ['required_items'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            

            
            <div class="col-md-12">
                <label for="description" class="form-label form-input-labels">Description<span
                        class="text-danger">*</span></label>
                <textarea class="form-control form-inputs-field form-textarea-field" id="description" name="description"
                    rows="4" placeholder="Enter Description here"><?php echo e(old('description', $service->description ?? '')); ?></textarea>
                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            

            
            <div class="col-md-12">
                <label class="form-label form-input-labels">Service Location<span class="text-danger">*</span></label>
                <div class="d-flex gap-4">
                    <label class="styled-checkbox d-flex gap-3">
                        <input type="checkbox" name="is_onsite" value="1" id="onsite-secondary"
                            <?php if(old('is_onsite', $service->is_onsite ?? false)): echo 'checked'; endif; ?>>
                        <span class="fs-14 light-black normal">On-site</span>
                    </label>

                    <label class="styled-checkbox d-flex gap-3">
                        <input type="checkbox" name="is_customer_location" value="1" <?php if(old('is_customer_location', $service->is_customer_location ?? false)): echo 'checked'; endif; ?>
                            id="customer-location-secondary">
                        <span class="fs-14 light-black normal">Customer Location</span>
                    </label>
                </div>
                <?php $__errorArgs = ['is_onsite'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            

            <div class="row row-gap-5">
                
                <div class="col-md-12 form-hide-box" id="physical-location-field">
                    <label for="pac-input" class="form-label form-input-labels">
                        Physical Location
                    </label>
                    <input type="text" class="form-control form-inputs-field form-textarea-field" id="pac-input"
                        name="physical_location" value="<?php echo e(old('physical_location')); ?>"
                        placeholder="Your registered business location">

                    <input type="hidden" name="lat" value="<?php echo e(old('lat')); ?>" id="latitude">

                    <input type="hidden" name="lng" value="<?php echo e(old('lng')); ?>" id="longitude">

                    <div class="custom_loc mt-2">
                        <div id="map" style="height: 300px;"></div>
                    </div>

                    <?php $__errorArgs = ['physical_location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-danger"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                

                
                <div class="col-md-4 form-hide-box" id="radius-field">
                    <label for="radius" class="form-label form-input-labels">Radius</label>
                    <input type="number" class="form-control form-inputs-field" placeholder="Enter radius"
                        value="<?php echo e(old('radius', $service->radius ?? '')); ?>" id="radius" name="radius">
                    <?php $__errorArgs = ['radius'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-danger">
                            <?php echo e($message); ?>

                        </p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="col-md-4 form-hide-box" id="traveltime-field">
                    <label for="traveltime" class="form-label form-input-labels">Travel
                        Time</label>
                    <input type="number" class="form-control form-inputs-field"
                        value="<?php echo e(old('travel_time', $service->travel_time ?? '')); ?>" placeholder="Enter travel time"
                        id="traveltime" name="travel_time">
                    <?php $__errorArgs = ['travel_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-danger">
                            <?php echo e($message); ?>

                        </p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="col-md-4 form-hide-box" id="servicecharges-field">
                    <label for="servicecharges" class="form-label form-input-labels">Additional
                        Service Charges</label>
                    <input type="number" class="form-control form-inputs-field"
                        placeholder="Enter additional service charges" id="servicecharges" name="service_charges"
                        value="<?php echo e(old('service_charges', $service->service_charges ?? '')); ?>">
                    <?php $__errorArgs = ['service_charges'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-danger">
                            <?php echo e($message); ?>

                        </p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            
            <div class="col-md-4 ">
                <label for="thumbnail-secondary" class="form-label form-input-labels">Thumbnail
                    Image<span class="text-danger">*</span></label>
                <div class="position-relative  form-add-category">
                    <div class="image-input <?php echo e($service->image ?? null ? 'image-input-changed' : 'image-input-empty'); ?>"
                        data-kt-image-input="true">
                        <div class="image-input-wrapper"
                            style="background-image: url('<?php echo e(asset('website') . '/' . ($service->image ?? '')); ?>');">
                        </div>
                        <label
                            class="image-label flex-column gap-3 align-items-center justify-content-center btn-active-color-primary shadow"
                            data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Change avatar">
                            <i class="bi bi-upload upload-icon"></i>
                            <span>Upload Image</span>
                            <span>50x50 px</span>
                            <input type="file" name="thumbnail" accept=".png, .jpg, .jpeg" />
                        </label>

                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Cancel avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="remove" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Remove avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                    </div>
                    <label id="thumbnail-error" class="error" for="thumbnail"></label>
                </div>
                <?php $__errorArgs = ['thumbnail'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            
            <div class="">
                <button type="submit" class="add-btn">
                    <?php echo e($btn_text ?? 'Add'); ?>

                </button>
            </div>
        </div>
    </form>
</div>




<?php $__env->startPush('js'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <script>
        // Wrap everything in IIFE to prevent variable conflicts
        (function() {
            const weekData = {};
            let currentWeekIndex = 0;
            const baseStartDate = moment().startOf(
                'isoWeek'); // This will set the start date to the current week's Monday

            // 🎯 JSON DATA INITIALIZATION - Pass your array JSON data here
            const initializeDataFromJSON = () => {
                // 📋 YOUR JSON DATA - Replace this array with your actual API response
                const availabilityArray = <?php echo \Illuminate\Support\Js::from(isset($service) ? $service->availabilities : [])->toHtml() ?>;

                // Convert array format to week-based format for the calendar
                availabilityArray.forEach(item => {
                    const date = moment(item.date);
                    const today = moment().startOf('day');

                    // 🚫 SKIP PAST DATES - Don't process dates that are in the past
                    if (date.isBefore(today, 'day')) {
                        return; // Skip this iteration for past dates
                    }

                    const weekStart = date.clone().startOf('isoWeek'); // Get Monday of that week
                    const weekKey = weekStart.format("YYYY-MM-DD");
                    const dayName = item.day;

                    // Convert time format from "HH:MM:SS" to "HH:MM"
                    const startTime = item.start_time.substring(0, 5); // Remove seconds
                    const endTime = item.end_time.substring(0, 5); // Remove seconds

                    // Initialize week if it doesn't exist
                    if (!weekData[weekKey]) {
                        weekData[weekKey] = {
                            Monday: {
                                enabled: false,
                                start: "10:00",
                                end: "19:00"
                            },
                            Tuesday: {
                                enabled: false,
                                start: "10:00",
                                end: "19:00"
                            },
                            Wednesday: {
                                enabled: false,
                                start: "10:00",
                                end: "19:00"
                            },
                            Thursday: {
                                enabled: false,
                                start: "10:00",
                                end: "19:00"
                            },
                            Friday: {
                                enabled: false,
                                start: "10:00",
                                end: "19:00"
                            },
                            Saturday: {
                                enabled: false,
                                start: "10:00",
                                end: "19:00"
                            },
                            Sunday: {
                                enabled: false,
                                start: "10:00",
                                end: "19:00"
                            }
                        };
                    }

                    // Set the specific day data (only for current and future dates)
                    weekData[weekKey][dayName] = {
                        enabled: true,
                        start: startTime,
                        end: endTime,
                        id: item.id, // Store original ID for reference
                        service_id: item.service_id // Store service_id for reference
                    };
                });
            };

            // 🎯 EXTRACT SELECTED AVAILABILITY IN YOUR DESIRED FORMAT
            const getSelectedAvailability = () => {
                const selectedAvailability = [];

                // Loop through all weeks in weekData
                Object.keys(weekData).forEach(weekKey => {
                    const weekStart = moment(weekKey); // Monday of the week
                    const weekDays = weekData[weekKey];

                    // Check each day of the week
                    Object.keys(weekDays).forEach(dayName => {
                        const dayData = weekDays[dayName];

                        // Only include enabled days
                        if (dayData.enabled) {
                            // Calculate the actual date for this day
                            const dayIndex = ["Monday", "Tuesday", "Wednesday", "Thursday",
                                "Friday",
                                "Saturday", "Sunday"
                            ].indexOf(dayName);
                            const actualDate = weekStart.clone().add(dayIndex, 'days');

                            // Add to result array in your desired format
                            selectedAvailability.push({
                                "date": actualDate.format("YYYY-MM-DD"),
                                "day": dayName,
                                "start": dayData.start,
                                "end": dayData.end
                            });
                        }
                    });
                });

                // Sort by date for better organization
                selectedAvailability.sort((a, b) => moment(a.date).diff(moment(b.date)));
                return selectedAvailability;
            };

            // 📝 UPDATE TEXTAREA WITH JSON OUTPUT
            const updateJsonOutput = () => {
                const selectedData = getSelectedAvailability();
                const jsonString = JSON.stringify(selectedData, null, 2);
                $("#jsonOutput").val(jsonString);
            };

            const updateWeekUI = () => {
                const startOfWeek = baseStartDate.clone().add(currentWeekIndex * 7, "days");
                const weekDays = Array.from({
                    length: 7
                }, (_, i) => startOfWeek.clone().add(i, "days"));
                const weekRange = `${weekDays[0].format("DD MMM YYYY")} - ${weekDays[6].format("DD MMM YYYY")}`;
                const weekKey = startOfWeek.format("YYYY-MM-DD");
                const week = weekData[weekKey] || {};

                $("#weekRange").text(weekRange);

                const dayNames = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
                const today = moment().startOf('day');

                const html = dayNames.map((day, index) => {
                    const date = weekDays[index];
                    const formattedDate = date.format("YYYY-MM-DD");
                    const val = week[day] || {
                        start: "10:00",
                        end: "19:00",
                        enabled: false
                    };

                    // 🚫 CHECK IF DATE IS IN THE PAST
                    const isPastDate = date.isBefore(today, 'day');
                    return `
                    <div class="d-flex align-items-center mb-2 day-row" data-day="${day}" data-date="${formattedDate}">
                        <input type="checkbox" class="form-check-input me-2 day-checkbox" data-day="${day}"
                               ${val.enabled ? "checked" : ""} ${isPastDate ? "disabled" : ""}>
                        <label class="me-2 ${isPastDate ? 'text-muted' : ''}" style="width: 100px;">${day}</label>
                        ${val.enabled && !isPastDate ? `
                                                                                    <input type="time" class="form-control form-control-sm me-2 start-time" value="${val.start}" data-day="${day}" style="width:120px;">
                                                                                    <span class="me-2">to</span>
                                                                                    <input type="time" class="form-control form-control-sm end-time" value="${val.end}" data-day="${day}" style="width:120px;">
                                                                                ` : `<span class="text-muted">${isPastDate ? "Past Date" : "Closed"}</span>`}
                    </div>`;
                }).join("");

                $("#weekDaysContainer").html(html);
            };

            const saveCurrentWeekData = () => {
                const base = baseStartDate.clone().add(currentWeekIndex * 7, "days");
                const key = base.format("YYYY-MM-DD");
                weekData[key] = {};

                $(".day-row").each(function() {
                    const day = $(this).data("day");
                    const enabled = $(this).find(".day-checkbox").is(":checked");
                    const start = $(this).find(".start-time").val() || "10:00";
                    const end = $(this).find(".end-time").val() || "19:00";
                    weekData[key][day] = {
                        enabled,
                        start,
                        end
                    };
                });
            };

            // Function to duplicate the weeks (with reset functionality)
            const duplicateWeeks = (weeks) => {
                const current = baseStartDate.clone().add(currentWeekIndex * 7, "days");
                const srcKey = current.format("YYYY-MM-DD");

                // 🔄 RESET: Clear all future week duplications first
                Object.keys(weekData).forEach(weekKey => {
                    const weekDate = moment(weekKey);
                    const currentWeekDate = moment(srcKey);

                    // Remove any week that's after the current week and was previously duplicated
                    if (weekDate.isAfter(currentWeekDate, 'week')) {
                        // Check if this week has the same pattern as current week (indicating it was duplicated)
                        const currentWeekData = weekData[srcKey];
                        const weekToCheck = weekData[weekKey];

                        // Only remove if it looks like a duplication (same enabled pattern)
                        if (currentWeekData && weekToCheck) {
                            const currentEnabledDays = Object.keys(currentWeekData).filter(day =>
                                currentWeekData[
                                    day].enabled);
                            const checkEnabledDays = Object.keys(weekToCheck).filter(day => weekToCheck[day]
                                .enabled);

                            // If same number of enabled days, likely a duplication - remove it
                            if (currentEnabledDays.length === checkEnabledDays.length && currentEnabledDays
                                .length >
                                0) {
                                delete weekData[weekKey];
                            }
                        }
                    }
                });

                // 📅 CREATE: Now create fresh duplications for the selected weeks
                for (let i = 1; i < weeks; i++) {
                    const next = current.clone().add(i * 7, "days");
                    const newKey = next.format("YYYY-MM-DD");
                    // Create deep copy of current week's data
                    weekData[newKey] = JSON.parse(JSON.stringify(weekData[srcKey]));
                }

            };

            // Validate time inputs
            const validateTimeInput = (input) => {
                const $input = $(input);
                const startTime = $input.val();
                const endTimeInput = $input.hasClass('start-time') ?
                    $input.closest('.day-row').find('.end-time') :
                    $input.closest('.day-row').find('.start-time');
                const endTime = endTimeInput.val();

                // Basic validation to ensure start time is before end time
                if (startTime && endTime && startTime >= endTime) {
                    alert('Start time must be before end time');
                    $input.focus();
                }
            };

            $(document).ready(function() {
                setTimeout(function() {
                    if (typeof $.fn.validate !== 'undefined') {
                        $.validator.addMethod('maxFileSize', function(value, element, maxSizeKB) {
                            if (element.files.length === 0) {
                                return true; // no file selected, let 'required' rule handle this
                            }
                            const fileSizeKB = element.files[0].size / 1024; // size in KB
                            return fileSizeKB <= maxSizeKB;
                        }, 'File size must be less than {0} KB.');
                        // Add custom validation method for role-based requirements
                        $.validator.addMethod('requiredForRole', function(value, element, params) {
                            const userRole = params.role;
                            const currentUserRole =
                                '<?php echo e(auth()->user()->getRoleNames()->first()); ?>';

                            // If current user has the specified role, field is required
                            if (currentUserRole === userRole) {
                                return value && value.length > 0;
                            }
                            // If user doesn't have the role, field is not required
                            return true;
                        }, 'This field is required for your role.');

                        // Add custom validation method for conditional requirements based on checkboxes
                        $.validator.addMethod('requiredIfChecked', function(value, element, params) {
                            const checkboxSelector = params.checkbox;
                            const isChecked = $(checkboxSelector).is(':checked');

                            // If checkbox is checked, field is required
                            if (isChecked) {
                                return value && value.trim().length > 0;
                            }
                            // If checkbox is not checked, field is not required
                            return true;
                        }, 'This field is required when the related option is selected.');



                        // Add validation for multiple roles
                        $.validator.addMethod('requiredForRoles', function(value, element, params) {
                            const requiredRoles = params.roles; // Array of roles
                            const currentUserRole =
                                '<?php echo e(auth()->user()->getRoleNames()->first()); ?>';

                            // If current user has any of the specified roles, field is required
                            if (requiredRoles.includes(currentUserRole)) {
                                return value && value.length > 0;
                            }
                            return true;
                        }, 'This field is required for your role.');

                        $("#individualServiceForm").validate({
                            submitHandler: function(form) {
                                // Check checkboxes before submitting
                                const isOnsiteChecked = $('#onsite-secondary').is(':checked');
                                const isCustomerLocationChecked = $('#customer-location-secondary').is(':checked');

                                if (!isOnsiteChecked && !isCustomerLocationChecked) {
                                    // Remove any existing error messages
                                    $('.service-location-error').remove();

                                    // Add single error message
                                    const errorMsg = '<div class="service-location-error" style="color: #dc3545; font-weight: bold; margin-top: 5px; font-size: 14px;">Please select at least one service location option (On-site or Customer Location)</div>';
                                    $('.d-flex.gap-4').after(errorMsg);

                                    return false; // Prevent form submission
                                }

                                console.log('Form validation passed, submitting...');
                                // Update availability data
                                saveCurrentWeekData();
                                var selectedData = getSelectedAvailability();
                                // Add availability data to form
                                var availabilityInput = $('<input>').attr({
                                    type: 'hidden',
                                    name: 'availabilities_dates',
                                    value: JSON.stringify(selectedData)
                                });
                                $(form).find('input[name="availabilities_dates"]').remove();
                                $(form).append(availabilityInput);
                                form.submit();
                            },
                            rules: {
                                thumbnail: {
                                    required: true,
                                    maxFileSize: 5120
                                },
                                name: {
                                    required: true,
                                    minlength: 2
                                },
                                category_id: {
                                    required: true
                                },
                                subcategory_id: {
                                    required: true
                                },
                                // Role-based validation for staff members
                                'staff_ids[]': {
                                    requiredForRole: {
                                        role: 'business'
                                    }
                                },

                                // Conditional validation for location fields
                                physical_location: {
                                    requiredIfChecked: {
                                        checkbox: '#onsite-secondary'
                                    }
                                },
                                radius: {
                                    requiredIfChecked: {
                                        checkbox: '#customer-location-secondary'
                                    },
                                    number: true,
                                    min: 1
                                },
                                travel_time: {
                                    requiredIfChecked: {
                                        checkbox: '#customer-location-secondary'
                                    },
                                    number: true,
                                    min: 1
                                },
                                service_charges: {
                                    requiredIfChecked: {
                                        checkbox: '#customer-location-secondary'
                                    },
                                    number: true,
                                    min: 0
                                },
                                duration: {
                                    required: true
                                },
                                price: {
                                    required: true,
                                    number: true,
                                    min: 0
                                },
                                additional_cost: {
                                    required: true,
                                    number: true,
                                    min: 0
                                },
                                required_items: {
                                    required: true,
                                    maxlength: 1000
                                },
                                description: {
                                    required: true,
                                    maxlength: 1000
                                }
                            },
                            messages: {
                                thumbnail: {
                                    required: "Please upload a thumbnail",
                                    maxFileSize: "Image size must not exceed 5 MB"
                                },
                                name: {
                                    required: "Service name is required",
                                    minlength: "Service name must be at least 2 characters"
                                },
                                category_id: {
                                    required: "Please select a category"
                                },
                                subcategory_id: {
                                    required: "Please select a subcategory"
                                },
                                'staff_ids[]': {
                                    requiredForRole: "Please assign at least one staff member to this service"
                                },

                                physical_location: {
                                    requiredIfChecked: "Physical location is required when On-site is selected"
                                },
                                radius: {
                                    requiredIfChecked: "Radius is required when Customer Location is selected",
                                    number: "Please enter a valid radius",
                                    min: "Radius must be at least 1"
                                },
                                travel_time: {
                                    requiredIfChecked: "Travel time is required when Customer Location is selected",
                                    number: "Please enter a valid travel time",
                                    min: "Travel time must be at least 1 minute"
                                },
                                service_charges: {
                                    requiredIfChecked: "Service charges are required when Customer Location is selected",
                                    number: "Please enter a valid service charge",
                                    min: "Service charges cannot be negative"
                                },
                                duration: {
                                    required: "Please select service duration"
                                },
                                price: {
                                    required: "Service price is required",
                                    number: "Please enter a valid price",
                                    min: "Price cannot be negative"
                                },
                                additional_cost: {
                                    required: "Additional cost is required",
                                    number: "Please enter a valid additional cost",
                                    min: "Additional cost cannot be negative"
                                },
                                required_items: {
                                    required: "Required items is required",
                                    maxlength: "Required items cannot exceed 1000 characters"
                                },
                                description: {
                                    required: "Description is required",
                                    maxlength: "Description cannot exceed 1000 characters"
                                }
                            }
                        });

                        $('#onsite-secondary, #customer-location-secondary').on('change', function() {
                            $('#pac-input, #radius, #traveltime, #servicecharges').removeClass(
                                'error');
                            $('.error[for="physical_location"], .error[for="radius"], .error[for="travel_time"], .error[for="service_charges"]')
                                .remove();

                            // Clear custom checkbox error message
                            $('.service-location-error').remove();
                        });
                    } else {
                        console.error('jQuery validate is not available');
                    }
                }, 500);

                // Initialize data from JSON
                initializeDataFromJSON();
                updateWeekUI();
                updateJsonOutput();


                $(document).on("change", ".day-checkbox", function() {
                    saveCurrentWeekData();
                    updateWeekUI();
                    updateJsonOutput(); // Update JSON when checkbox changes
                });

                // Validate time inputs when they change
                $(document).on("change", ".start-time, .end-time", function() {
                    validateTimeInput(this);
                    saveCurrentWeekData();
                    updateJsonOutput(); // Update JSON when time changes
                });

                $("#prevWeek").click(function() {
                    saveCurrentWeekData();
                    currentWeekIndex--;
                    updateWeekUI();
                    updateJsonOutput(); // Update JSON when week changes
                });

                $("#nextWeek").click(function() {
                    saveCurrentWeekData();
                    currentWeekIndex++;
                    updateWeekUI();
                    updateJsonOutput(); // Update JSON when week changes
                });

                $("#saveAvailability").click(function() {
                    saveCurrentWeekData();
                    updateJsonOutput(); // Final update of JSON

                    const selectedData = getSelectedAvailability();
                    alert("Availability Saved!");
                });

                // Recurring Radio Button Change
                $("input[name='recurring']").change(function() {
                    const selected = $(this).val();
                    saveCurrentWeekData();

                    if (selected === "custom") {
                        $(".custom-weeks-input").show();
                    } else {
                        $(".custom-weeks-input").hide();
                    }

                    if (selected === "custom") {
                        return false;
                    }

                    const repeatWeeks = parseInt(selected);

                    if (repeatWeeks > 0) {
                        duplicateWeeks(repeatWeeks);
                        updateJsonOutput(); // Update JSON after duplication
                        alert(`Availability reset and duplicated for ${repeatWeeks} weeks total.`);
                    }
                });

                // Handle Custom Weeks Input
                $("#customDone").click(function() {
                    const customWeeks = parseInt($("#customWeeks").val());
                    if (!isNaN(customWeeks) && customWeeks > 0) {
                        saveCurrentWeekData();
                        duplicateWeeks(customWeeks);
                        updateJsonOutput(); // Update JSON after custom duplication
                        alert(`Availability reset and duplicated for ${customWeeks} weeks total.`);
                        $(".custom-weeks-input").hide();
                        $("input[name='recurring']").prop('checked', false); // Uncheck radio buttons
                    } else {
                        alert("Please enter a valid number of weeks.");
                    }
                });
            });
        })(); // Close IIFE

        // Initialize Google Maps for Individual Service
        function initIndividualServiceMap() {
            // Only use existing coordinates if we have them in the form fields (from old input or editing)
            const existingLat = document.getElementById('latitude').value;
            const existingLng = document.getElementById('longitude').value;
            const existingLocation = document.getElementById('pac-input').value;

            const userLat = existingLat && existingLat !== '' ? parseFloat(existingLat) : null;
            const userLng = existingLng && existingLng !== '' ? parseFloat(existingLng) : null;
            const userLocation = existingLocation || '';



            let map, marker;

            // Only initialize map if we have existing coordinates
            if (userLat !== null && userLng !== null) {

                // Initialize map with existing coordinates
                map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 15,
                    center: {
                        lat: userLat,
                        lng: userLng
                    }
                });

                // Create marker at existing location
                marker = new google.maps.Marker({
                    position: {
                        lat: userLat,
                        lng: userLng
                    },
                    map: map,
                    draggable: true,
                    title: userLocation || 'Your Business Location'
                });

                // Set the form values
                document.getElementById('latitude').value = userLat;
                document.getElementById('longitude').value = userLng;
            } else {

                // Show placeholder instead of map
                const mapElement = document.getElementById('map');
                mapElement.innerHTML =
                    '<div style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border: 2px dashed #dee2e6; color: #6c757d; text-align: center;"><div><i class="fas fa-map-marker-alt" style="font-size: 2rem; margin-bottom: 10px;"></i><br>Map will appear after you search for a location</div></div>';
            }

            // Initialize autocomplete
            const input = document.getElementById('pac-input');
            const autocomplete = new google.maps.places.Autocomplete(input);

            // Only bind to map bounds if map exists
            if (map) {
                autocomplete.bindTo('bounds', map);
            }

            // Handle place selection
            autocomplete.addListener('place_changed', function() {
                const place = autocomplete.getPlace();
                if (!place.geometry) {
                    return;
                }

                // Initialize map if it doesn't exist yet
                if (!map) {
                    map = new google.maps.Map(document.getElementById('map'), {
                        zoom: 15,
                        center: place.geometry.location
                    });

                    marker = new google.maps.Marker({
                        position: place.geometry.location,
                        map: map,
                        draggable: true,
                        title: 'Selected Location'
                    });

                    // Add drag listener for new marker
                    marker.addListener('dragend', function() {
                        const position = marker.getPosition();
                        document.getElementById('latitude').value = position.lat();
                        document.getElementById('longitude').value = position.lng();

                        // Reverse geocoding to update address
                        const geocoder = new google.maps.Geocoder();
                        geocoder.geocode({
                            location: position
                        }, function(results, status) {
                            if (status === 'OK' && results[0]) {
                                document.getElementById('pac-input').value = results[0]
                                    .formatted_address;
                            }
                        });
                    });
                } else {
                    // Update existing map and marker
                    if (place.geometry.viewport) {
                        map.fitBounds(place.geometry.viewport);
                    } else {
                        map.setCenter(place.geometry.location);
                        map.setZoom(17);
                    }
                    marker.setPosition(place.geometry.location);
                }

                // Update hidden inputs
                document.getElementById('latitude').value = place.geometry.location.lat();
                document.getElementById('longitude').value = place.geometry.location.lng();
            });

            // Handle marker drag (only if marker exists)
            if (marker) {
                marker.addListener('dragend', function() {
                    const position = marker.getPosition();
                    document.getElementById('latitude').value = position.lat();
                    document.getElementById('longitude').value = position.lng();

                    // Reverse geocoding to update address
                    const geocoder = new google.maps.Geocoder();
                    geocoder.geocode({
                        location: position
                    }, function(results, status) {
                        if (status === 'OK' && results[0]) {
                            document.getElementById('pac-input').value = results[0].formatted_address;
                        }
                    });
                });
            }
        }

        // Initialize when Google Maps is ready
        $(document).ready(function() {
            // Wait for Google Maps to load, then initialize
            function waitForGoogleMaps() {
                if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
                    initIndividualServiceMap();
                } else {
                    setTimeout(waitForGoogleMaps, 100);
                }
            }
            waitForGoogleMaps();
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/service/include/individual.blade.php ENDPATH**/ ?>